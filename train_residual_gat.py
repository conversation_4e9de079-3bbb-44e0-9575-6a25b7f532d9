import os
import glob
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch_geometric.nn import GATConv, global_mean_pool, global_max_pool
from torch_geometric.data import Data, DataLoader
from sklearn.model_selection import train_test_split, StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import scipy.io as sio

# 设置随机种子
torch.manual_seed(42)
np.random.seed(42)

class ResidualGATBlock(nn.Module):
    """
    残差GAT块
    """
    def __init__(self, in_channels, out_channels, heads=1, dropout=0.3, use_edge_weights=True):
        super(ResidualGATBlock, self).__init__()
        self.use_edge_weights = use_edge_weights
        
        # GAT层
        if use_edge_weights:
            self.gat = GATConv(in_channels, out_channels, heads=heads, concat=False, edge_dim=1, dropout=dropout)
        else:
            self.gat = GATConv(in_channels, out_channels, heads=heads, concat=False, dropout=dropout)
        
        # 残差连接的投影层（如果输入输出维度不同）
        self.residual_proj = nn.Linear(in_channels, out_channels) if in_channels != out_channels else None
        
        # 层归一化
        self.layer_norm = nn.LayerNorm(out_channels)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
        
    def forward(self, x, edge_index, edge_attr=None):
        # 保存残差
        residual = x
        
        # GAT层
        if self.use_edge_weights and edge_attr is not None:
            x = self.gat(x, edge_index, edge_attr=edge_attr)
        else:
            x = self.gat(x, edge_index)
        
        # 残差投影（如果需要）
        if self.residual_proj is not None:
            residual = self.residual_proj(residual)
        
        # 残差连接
        x = x + residual
        
        # 层归一化
        x = self.layer_norm(x)
        
        # 激活函数
        x = F.relu(x)
        
        # Dropout
        x = self.dropout(x)
        
        return x

class AdvancedResidualGAT(nn.Module):
    """
    高级残差GAT模型，支持多种配置
    """
    def __init__(self, num_node_features, hidden_channels, num_classes, 
                 num_layers=3, heads=1, dropout=0.3, use_edge_weights=True,
                 use_skip_connections=True, use_attention_pooling=False):
        super(AdvancedResidualGAT, self).__init__()
        
        self.num_layers = num_layers
        self.use_skip_connections = use_skip_connections
        self.use_attention_pooling = use_attention_pooling
        
        # 输入投影层
        self.input_proj = nn.Linear(num_node_features, hidden_channels)
        
        # 残差GAT块
        self.gat_blocks = nn.ModuleList()
        for i in range(num_layers):
            self.gat_blocks.append(
                ResidualGATBlock(
                    hidden_channels, 
                    hidden_channels, 
                    heads=heads, 
                    dropout=dropout,
                    use_edge_weights=use_edge_weights
                )
            )
        
        # 跳跃连接的权重（如果使用）
        if use_skip_connections:
            self.skip_weights = nn.Parameter(torch.ones(num_layers + 1))
        
        # 注意力池化层（可选）
        if use_attention_pooling:
            self.attention_pool = nn.Sequential(
                nn.Linear(hidden_channels, hidden_channels // 2),
                nn.Tanh(),
                nn.Linear(hidden_channels // 2, 1)
            )
        
        # 分类器
        final_dim = hidden_channels * 2 if use_attention_pooling else hidden_channels
        self.classifier = nn.Sequential(
            nn.Linear(final_dim, hidden_channels),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels, hidden_channels // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_channels // 2, num_classes)
        )
    
    def forward(self, x, edge_index, edge_attr, batch):
        # 输入投影
        x = self.input_proj(x)
        
        # 保存所有层的输出（用于跳跃连接）
        layer_outputs = [x]
        
        # 通过残差GAT块
        for gat_block in self.gat_blocks:
            x = gat_block(x, edge_index, edge_attr)
            layer_outputs.append(x)
        
        # 跳跃连接（加权组合所有层的输出）
        if self.use_skip_connections:
            # 归一化权重
            weights = F.softmax(self.skip_weights, dim=0)
            x = sum(w * output for w, output in zip(weights, layer_outputs))
        
        # 图池化
        if self.use_attention_pooling:
            # 注意力池化
            attention_scores = self.attention_pool(x)
            attention_weights = F.softmax(attention_scores, dim=0)
            
            # 加权平均池化
            x_att = global_mean_pool(x * attention_weights, batch)
            # 普通平均池化
            x_mean = global_mean_pool(x, batch)
            # 最大池化
            x_max = global_max_pool(x, batch)
            
            # 组合不同的池化结果
            x = torch.cat([x_att, x_mean], dim=1)
        else:
            # 简单的平均池化
            x = global_mean_pool(x, batch)
        
        # 分类
        x = self.classifier(x)
        
        return x

class SimpleResidualGAT(nn.Module):
    """
    简化的残差GAT模型，与原GCN结构更接近
    """
    def __init__(self, num_node_features, hidden_channels, num_classes):
        super(SimpleResidualGAT, self).__init__()
        
        # 输入投影
        self.input_proj = nn.Linear(num_node_features, hidden_channels)
        
        # 两层残差GAT
        self.gat_block1 = ResidualGATBlock(hidden_channels, hidden_channels, heads=1, dropout=0.5, use_edge_weights=True)
        self.gat_block2 = ResidualGATBlock(hidden_channels, hidden_channels, heads=1, dropout=0.5, use_edge_weights=True)
        
        # 分类层
        self.classifier = nn.Linear(hidden_channels, num_classes)
    
    def forward(self, x, edge_index, edge_attr, batch):
        # 输入投影
        x = self.input_proj(x)
        
        # 残差GAT块
        x = self.gat_block1(x, edge_index, edge_attr)
        x = self.gat_block2(x, edge_index, edge_attr)
        
        # 图池化
        x = global_mean_pool(x, batch)
        
        # 分类
        x = self.classifier(x)
        
        return x

def load_node_features(subject_id, label):
    """
    加载特定被试的节点特征
    """
    if label == 0:
        feature_file = os.path.join('node_features', 'HC', f'ROISignals_{subject_id}.mat')
    elif label == 1:
        feature_file = os.path.join('node_features', 'MCI', f'ROISignals_{subject_id}.mat')
    else:
        raise ValueError(f"未知的标签类型: {label}")
    
    if not os.path.exists(feature_file):
        raise FileNotFoundError(f"节点特征文件不存在: {feature_file}")
    
    try:
        mat_data = sio.loadmat(feature_file)
        
        if 'ROISignals' in mat_data:
            signals = mat_data['ROISignals']
            
            # 计算统计特征
            mean = np.mean(signals, axis=0).reshape(-1, 1)
            std = np.std(signals, axis=0).reshape(-1, 1)
            min_val = np.min(signals, axis=0).reshape(-1, 1)
            max_val = np.max(signals, axis=0).reshape(-1, 1)
            
            features = np.hstack([mean, std, min_val, max_val])
            return features
        else:
            return None
            
    except Exception as e:
        print(f"读取文件 {feature_file} 时出错: {str(e)}")
        return None

def load_data():
    """
    加载数据
    """
    label_file = 'edge_lists/subject_labels.csv'
    if not os.path.exists(label_file):
        raise FileNotFoundError(f"标签文件不存在: {label_file}")
    
    label_df = pd.read_csv(label_file)
    print(f"标签文件加载完成，包含 {len(label_df)} 个被试")

    hc_files = sorted(glob.glob('edge_lists/HC/*_gcn_data.npz'))
    mci_files = sorted(glob.glob('edge_lists/MCI/*_gcn_data.npz'))
    all_files = hc_files + mci_files
    
    graph_data = []
    labels = []
    subject_ids = []
    
    for file_path in all_files:
        subject_id = os.path.basename(file_path).split('_gcn_data.npz')[0]

        try:
            subject_id_int = int(subject_id)
        except ValueError:
            continue

        subject_label = label_df[label_df['subject_id'] == subject_id_int]['label'].values
        if len(subject_label) == 0:
            continue
        label = subject_label[0]
        
        npz_data = np.load(file_path)
        edge_index = torch.tensor(npz_data['edge_index'].T, dtype=torch.long)
        edge_attr = torch.tensor(npz_data['edge_attr'], dtype=torch.float)
        
        num_nodes = edge_index.max().item() + 1
        
        node_features = load_node_features(subject_id, label)
        
        if node_features is not None:
            if node_features.shape[0] != num_nodes:
                if node_features.shape[0] > num_nodes:
                    node_features = node_features[:num_nodes, :]
                else:
                    padding = np.zeros((num_nodes - node_features.shape[0], node_features.shape[1]))
                    node_features = np.vstack([node_features, padding])
                    
            x = torch.tensor(node_features, dtype=torch.float)
        else:
            degrees = torch.zeros(num_nodes, dtype=torch.float)
            for node in edge_index[0]:
                degrees[node] += 1
            x = degrees.view(-1, 1)
        
        data = Data(x=x, edge_index=edge_index, edge_attr=edge_attr, y=torch.tensor([label], dtype=torch.long))
        
        graph_data.append(data)
        labels.append(label)
        subject_ids.append(subject_id)
    
    return graph_data, np.array(labels), subject_ids

def train_model(model, train_loader, device):
    """
    训练模型
    """
    model.train()
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=5e-4)
    criterion = torch.nn.CrossEntropyLoss()

    total_loss = 0
    for data in train_loader:
        data = data.to(device)
        optimizer.zero_grad()
        out = model(data.x, data.edge_index, data.edge_attr, data.batch)
        loss = criterion(out, data.y)
        loss.backward()
        optimizer.step()
        total_loss += loss.item() * data.num_graphs

    return total_loss / len(train_loader.dataset)

def evaluate_model(model, loader, device):
    """
    评估模型
    """
    model.eval()
    correct = 0
    predictions = []
    targets = []

    with torch.no_grad():
        for data in loader:
            data = data.to(device)
            out = model(data.x, data.edge_index, data.edge_attr, data.batch)
            pred = out.argmax(dim=1)
            correct += int((pred == data.y).sum())
            predictions.extend(pred.cpu().numpy())
            targets.extend(data.y.cpu().numpy())

    accuracy = correct / len(loader.dataset)
    f1 = f1_score(targets, predictions, average='macro')

    return accuracy, f1, predictions, targets

def cross_validation_residual_gat(graph_data, labels, subject_ids, n_splits=10):
    """
    使用交叉验证比较不同残差GAT配置
    """
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")

    num_node_features = graph_data[0].x.shape[1]
    print(f"节点特征维度: {num_node_features}")

    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)

    # 定义不同的模型配置
    model_configs = {
        'SimpleResidualGAT': lambda: SimpleResidualGAT(num_node_features, 64, 2),
        'ResidualGAT_3layers': lambda: AdvancedResidualGAT(
            num_node_features, 64, 2, num_layers=3, heads=1, dropout=0.3,
            use_edge_weights=True, use_skip_connections=True, use_attention_pooling=False
        ),
        'ResidualGAT_4layers': lambda: AdvancedResidualGAT(
            num_node_features, 64, 2, num_layers=4, heads=1, dropout=0.3,
            use_edge_weights=True, use_skip_connections=True, use_attention_pooling=False
        ),
        'ResidualGAT_MultiHead': lambda: AdvancedResidualGAT(
            num_node_features, 64, 2, num_layers=3, heads=2, dropout=0.3,
            use_edge_weights=True, use_skip_connections=True, use_attention_pooling=False
        ),
        'ResidualGAT_AttentionPool': lambda: AdvancedResidualGAT(
            num_node_features, 64, 2, num_layers=3, heads=1, dropout=0.3,
            use_edge_weights=True, use_skip_connections=True, use_attention_pooling=True
        ),
        'ResidualGAT_NoSkip': lambda: AdvancedResidualGAT(
            num_node_features, 64, 2, num_layers=3, heads=1, dropout=0.3,
            use_edge_weights=True, use_skip_connections=False, use_attention_pooling=False
        )
    }

    results = {name: {'accuracies': [], 'f1_scores': []} for name in model_configs.keys()}

    for fold, (train_idx, test_idx) in enumerate(skf.split(np.zeros(len(labels)), labels)):
        print(f"\n第 {fold+1}/{n_splits} 折交叉验证")

        train_data = [graph_data[i] for i in train_idx]
        test_data = [graph_data[i] for i in test_idx]

        train_loader = DataLoader(train_data, batch_size=32, shuffle=True)
        test_loader = DataLoader(test_data, batch_size=32, shuffle=False)

        for model_name, model_factory in model_configs.items():
            print(f"训练 {model_name}...")

            model = model_factory().to(device)

            best_val_acc = 0
            patience = 20
            patience_counter = 0
            epochs = 200

            for epoch in range(1, epochs + 1):
                loss = train_model(model, train_loader, device)
                train_acc, _, _, _ = evaluate_model(model, train_loader, device)
                val_acc, val_f1, _, _ = evaluate_model(model, test_loader, device)

                if epoch % 50 == 0:
                    print(f'  Epoch: {epoch:03d}, Loss: {loss:.4f}, Train Acc: {train_acc:.4f}, Val Acc: {val_acc:.4f}')

                if val_acc > best_val_acc:
                    best_val_acc = val_acc
                    patience_counter = 0
                    torch.save(model.state_dict(), f'{model_name}_fold{fold+1}.pt')
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        break

            # 加载最佳模型并评估
            model.load_state_dict(torch.load(f'{model_name}_fold{fold+1}.pt'))
            test_acc, test_f1, _, _ = evaluate_model(model, test_loader, device)

            results[model_name]['accuracies'].append(test_acc)
            results[model_name]['f1_scores'].append(test_f1)

            print(f'  {model_name} - 测试准确率: {test_acc:.4f}, F1: {test_f1:.4f}')

    # 输出结果
    print("\n" + "="*80)
    print("残差GAT模型性能比较")
    print("="*80)

    model_performance = []
    for model_name, metrics in results.items():
        acc_mean = np.mean(metrics['accuracies'])
        acc_std = np.std(metrics['accuracies'])
        f1_mean = np.mean(metrics['f1_scores'])
        f1_std = np.std(metrics['f1_scores'])

        model_performance.append({
            'name': model_name,
            'acc_mean': acc_mean,
            'acc_std': acc_std,
            'f1_mean': f1_mean,
            'f1_std': f1_std
        })

    # 按准确率排序
    model_performance.sort(key=lambda x: x['acc_mean'], reverse=True)

    for i, model in enumerate(model_performance):
        print(f"{i+1}. {model['name']}:")
        print(f"   准确率: {model['acc_mean']:.4f} ± {model['acc_std']:.4f}")
        print(f"   F1分数: {model['f1_mean']:.4f} ± {model['f1_std']:.4f}")
        print()

    return results

def main():
    print("加载数据...")
    graph_data, labels, subject_ids = load_data()
    print(f"数据加载完成，总共 {len(graph_data)} 个样本")
    print(f"HC样本数: {np.sum(labels == 0)}")
    print(f"MCI样本数: {np.sum(labels == 1)}")

    print("开始残差GAT模型比较...")
    cross_validation_residual_gat(graph_data, labels, subject_ids, n_splits=10)

if __name__ == "__main__":
    main()
